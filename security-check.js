#!/usr/bin/env node

/**
 * 🔒 UniScribe 安全检查脚本
 *
 * 这个脚本会检查常见的安全配置问题
 * 运行方式: node security-check.js
 */

const fs = require('fs');
const path = require('path');

// 动态导入 ES 模块
async function importSecurityConfig() {
  try {
    const { CSP_DOMAINS, getAllowedDomains } = await import('./src/config/security.js');
    return { CSP_DOMAINS, getAllowedDomains };
  } catch (error) {
    console.warn('无法导入安全配置，使用静态检查');
    return null;
  }
}

console.log('🔒 UniScribe 安全检查开始...\n');

const checks = [];
let passedChecks = 0;
let totalChecks = 0;

function addCheck(name, passed, message) {
  totalChecks++;
  if (passed) {
    passedChecks++;
    console.log(`✅ ${name}: ${message}`);
  } else {
    console.log(`❌ ${name}: ${message}`);
  }
  checks.push({ name, passed, message });
}

// 检查 1: .env 文件是否在 .gitignore 中
function checkEnvInGitignore() {
  try {
    const gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
    const hasEnv = gitignoreContent.includes('.env');
    addCheck(
      '环境变量保护',
      hasEnv,
      hasEnv ? '.env 文件已在 .gitignore 中' : '.env 文件未在 .gitignore 中，存在泄露风险'
    );
  } catch (error) {
    addCheck('环境变量保护', false, '无法读取 .gitignore 文件');
  }
}

// 检查 2: next.config.mjs 中是否有安全头部
async function checkSecurityHeaders() {
  try {
    const configContent = fs.readFileSync('next.config.mjs', 'utf8');
    const hasSecurityImport = configContent.includes('generateNextJSSecurityHeaders');
    const hasXFrameOptions = configContent.includes('X-Frame-Options') || hasSecurityImport;
    const hasCSP = configContent.includes('Content-Security-Policy') || hasSecurityImport;
    const hasHSTS = configContent.includes('Strict-Transport-Security') || hasSecurityImport;

    addCheck(
      '安全配置统一管理',
      hasSecurityImport,
      hasSecurityImport ? '使用统一的安全配置文件' : '建议使用统一的安全配置文件'
    );

    addCheck(
      '点击劫持防护',
      hasXFrameOptions,
      hasXFrameOptions ? 'X-Frame-Options 头部已配置' : '缺少 X-Frame-Options 头部'
    );

    addCheck(
      'CSP 防护',
      hasCSP,
      hasCSP ? 'Content-Security-Policy 已配置' : '缺少 Content-Security-Policy'
    );

    addCheck(
      'HTTPS 强制',
      hasHSTS,
      hasHSTS ? 'HSTS 头部已配置' : '缺少 HSTS 头部'
    );

    // 如果使用了统一配置，检查配置文件
    if (hasSecurityImport) {
      const securityConfig = await importSecurityConfig();
      if (securityConfig) {
        const { CSP_DOMAINS } = securityConfig;
        const requiredDomains = [
          'https://www.clarity.ms',
          'https://api-gateway.umami.dev',
          'https://cloud.umami.is',
          'https://pa.uniscribe.co'
        ];

        const allDomains = [
          ...CSP_DOMAINS.scriptSrc,
          ...CSP_DOMAINS.connectSrc
        ];

        const missingDomains = requiredDomains.filter(domain =>
          !allDomains.some(allowedDomain => allowedDomain.includes(domain.replace('https://', '')))
        );

        addCheck(
          'CSP 域名完整性',
          missingDomains.length === 0,
          missingDomains.length === 0 ? 'CSP 包含所有必要域名' : `CSP 缺少域名: ${missingDomains.join(', ')}`
        );
      }
    }
  } catch (error) {
    addCheck('安全头部配置', false, '无法读取配置文件: ' + error.message);
  }
}

// 检查 3: 中间件安全配置
function checkMiddlewareSecurity() {
  try {
    const middlewareContent = fs.readFileSync('src/middleware.js', 'utf8');
    const hasSecurityImport = middlewareContent.includes('setSecurityHeaders');
    const hasSecurityHeaders = middlewareContent.includes('X-Frame-Options') ||
      middlewareContent.includes('Content-Security-Policy') ||
      hasSecurityImport;

    addCheck(
      '中间件安全配置统一',
      hasSecurityImport,
      hasSecurityImport ? '中间件使用统一安全配置' : '建议中间件使用统一安全配置'
    );

    addCheck(
      '中间件安全',
      hasSecurityHeaders,
      hasSecurityHeaders ? '中间件安全头部已配置' : '中间件缺少安全头部'
    );
  } catch (error) {
    addCheck('中间件安全', false, '无法读取 middleware.js 文件');
  }
}

// 检查 4: 是否存在敏感文件
function checkSensitiveFiles() {
  const sensitiveFiles = ['.env'];
  let foundSensitive = false;

  sensitiveFiles.forEach(file => {
    if (fs.existsSync(file)) {
      foundSensitive = true;
      console.log(`⚠️  发现敏感文件: ${file} - 确保此文件不会被提交到代码仓库`);
    }
  });

  addCheck(
    '敏感文件检查',
    true, // 这个检查总是通过，只是提醒
    foundSensitive ? '发现敏感文件，请确保已在 .gitignore 中' : '未发现敏感文件'
  );
}

// 检查 5: package.json 中的安全相关依赖
function checkSecurityDependencies() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

    const hasHelmet = deps.helmet;
    const hasSentry = deps['@sentry/nextjs'];

    addCheck(
      'Sentry 错误监控',
      hasSentry,
      hasSentry ? 'Sentry 已配置用于错误监控' : '建议添加 Sentry 进行错误监控'
    );
  } catch (error) {
    addCheck('依赖检查', false, '无法读取 package.json 文件');
  }
}

// 检查 6: 文档结构
function checkDocumentationStructure() {
  const requiredDocs = [
    'docs/README.md',
    'docs/security/README.md',
    'docs/development/README.md',
    'docs/security/SECURITY_AUDIT_REPORT.md',
    'docs/security/SECURITY_IMPROVEMENTS.md',
    'docs/security/CSP_TROUBLESHOOTING_GUIDE.md'
  ];

  const missingDocs = requiredDocs.filter(doc => !fs.existsSync(doc));

  addCheck(
    '文档结构完整性',
    missingDocs.length === 0,
    missingDocs.length === 0 ? '所有必要文档都存在' : `缺少文档: ${missingDocs.join(', ')}`
  );

  // 检查根目录是否还有散落的 .md 文件（除了 README.md）
  try {
    const rootFiles = fs.readdirSync('.');
    const rootMdFiles = rootFiles.filter(file =>
      file.endsWith('.md') &&
      file !== 'README.md' &&
      !file.startsWith('.')
    );

    addCheck(
      '根目录整洁性',
      rootMdFiles.length === 0,
      rootMdFiles.length === 0 ? '根目录文档已整理' : `根目录仍有散落文档: ${rootMdFiles.join(', ')}`
    );
  } catch (error) {
    addCheck('根目录检查', false, '无法检查根目录文件');
  }
}

// 执行所有检查
async function runAllChecks() {
  checkEnvInGitignore();
  await checkSecurityHeaders();
  checkMiddlewareSecurity();
  checkSensitiveFiles();
  checkSecurityDependencies();
  checkDocumentationStructure();

  // 输出总结
  console.log('\n📊 安全检查总结:');
  console.log(`通过检查: ${passedChecks}/${totalChecks}`);

  const score = (passedChecks / totalChecks) * 100;
  console.log(`安全评分: ${score.toFixed(1)}%`);

  if (score >= 90) {
    console.log('🎉 安全配置优秀！');
  } else if (score >= 70) {
    console.log('👍 安全配置良好，但还有改进空间');
  } else {
    console.log('⚠️  安全配置需要改进');
  }

  // 输出建议
  console.log('\n💡 安全建议:');
  console.log('1. 定期更新依赖包: npm audit');
  console.log('2. 定期轮换 API 密钥');
  console.log('3. 监控异常访问日志');
  console.log('4. 部署后验证安全头部: curl -I https://your-domain.com');
  console.log('5. 考虑添加 rate limiting 防止暴力攻击');

  console.log('\n🔒 安全检查完成！');
}

// 运行检查
runAllChecks().catch(console.error);
