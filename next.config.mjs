import createNextIntlPlugin from "next-intl/plugin";
import { withSentryConfig } from "@sentry/nextjs";

// 开发环境简化的安全头部配置
const SECURITY_HEADERS = process.env.NODE_ENV === 'development'
  ? [
    // 开发环境：只保留基本的安全头部，允许所有资源
    {
      key: 'X-Frame-Options',
      value: 'SAMEORIGIN' // 开发环境允许同源嵌入
    }
  ]
  : [
    // 生产环境：完整的安全头部配置
    {
      key: 'X-Frame-Options',
      value: 'DENY'
    },
    {
      key: 'Content-Security-Policy',
      value: "frame-ancestors 'none'; default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cloud.umami.is https://pa.uniscribe.co https://www.googletagmanager.com https://www.google-analytics.com https://js.sentry-cdn.com https://www.clarity.ms https://tally.so; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://tally.so; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; media-src 'self' data: blob: https://shiyin.bf922d70183b00570ae0ab7e483fcf84.r2.cloudflarestorage.com; frame-src 'self' https://tally.so https://www.youtube.com https://youtube.com; connect-src 'self' https://api.uniscribe.co https://api-surge.uniscribe.co https://bgcrcrsosqswpseimvvx.supabase.co https://cloud.umami.is https://pa.uniscribe.co https://www.google-analytics.com https://o4508829103095808.ingest.us.sentry.io https://api-gateway.umami.dev https://*.clarity.ms https://tally.so https://shiyin.bf922d70183b00570ae0ab7e483fcf84.r2.cloudflarestorage.com; object-src 'none'; base-uri 'self'; form-action 'self';"
    },
    {
      key: 'Strict-Transport-Security',
      value: 'max-age=31536000; includeSubDomains; preload'
    },
    {
      key: 'X-Content-Type-Options',
      value: 'nosniff'
    },
    {
      key: 'Referrer-Policy',
      value: 'strict-origin-when-cross-origin'
    },
    {
      key: 'Permissions-Policy',
      value: 'camera=(), microphone=(), geolocation=()'
    }
  ];

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
        port: "",
        pathname: "/a/**",
      },
    ],
  },
  // 添加静态文件的缓存控制和安全头部
  async headers() {
    return [
      {
        // 为所有页面添加安全头部，防止点击劫持等攻击
        source: "/(.*)",
        headers: SECURITY_HEADERS,
      },
      {
        // 为FFmpeg文件添加缓存控制头
        source: "/ffmpeg/:path*",
        headers: [
          {
            key: "Cache-Control",
            // 设置为一年的缓存时间，并标记为immutable
            value: "public, max-age=31536000, immutable",
          },
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
        ],
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: "/api/:path*", // 前端请求的路径
        destination: "http://backend.uniscribe.dev:8000/:path*", // 代理到后端的路径
      },
    ];
  },
  async redirects() {
    return [
      {
        source: "/dashboard/:transcriptionId",
        destination: "/transcriptions/:transcriptionId",
        permanent: true, // 设置为永久重定向 (301)
      },
    ];
  },
};

// 先应用 nextIntl 插件，再应用 Sentry 配置
export default withSentryConfig(withNextIntl(nextConfig), {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  org: "uniscribe",
  project: "uniscribe-web",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Automatically annotate React components to show their full name in breadcrumbs and session replay
  reactComponentAnnotation: {
    enabled: true,
  },

  // Hides source maps from generated client bundles
  hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
});
