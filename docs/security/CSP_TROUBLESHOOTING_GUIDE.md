# 🛠️ Content Security Policy (CSP) 故障排除指南

## 🚨 常见 CSP 错误及解决方案

### 1. Script 被阻止错误
```
Refused to load the script 'https://example.com/script.js' because it violates the following Content Security Policy directive: "script-src ..."
```

**解决方案**: 在 CSP 的 `script-src` 中添加该域名

### 2. Connect 被阻止错误
```
Refused to connect to 'https://api.example.com' because it violates the following Content Security Policy directive: "connect-src ..."
```

**解决方案**: 在 CSP 的 `connect-src` 中添加该域名

### 3. Style 被阻止错误
```
Refused to apply style from 'https://fonts.googleapis.com' because its MIME type ('text/html') is not a supported stylesheet MIME type
```

**解决方案**: 在 CSP 的 `style-src` 中添加该域名

### 4. Media 被阻止错误
```
Refused to load media from 'https://example.com/audio.mp3' because it violates the following Content Security Policy directive: "default-src 'self'". Note that 'media-src' was not explicitly set, so 'default-src' is used as a fallback.
```

**解决方案**: 在 CSP 的 `media-src` 中添加该域名

### 5. Frame 被阻止错误
```
Refused to frame 'https://tally.so/' because it violates the following Content Security Policy directive: "default-src 'self'". Note that 'frame-src' was not explicitly set, so 'default-src' is used as a fallback.
```

**解决方案**: 在 CSP 的 `frame-src` 中添加该域名

## 🔧 快速修复步骤

### 步骤 1: 识别被阻止的资源
1. 打开浏览器开发者工具 (F12)
2. 查看 Console 标签页
3. 找到 CSP 相关的错误信息
4. 记录被阻止的域名和资源类型

### 步骤 2: 确定需要修改的 CSP 指令
| 资源类型 | CSP 指令 | 示例 |
|---------|---------|------|
| JavaScript 脚本 | `script-src` | `https://www.clarity.ms` |
| API 请求 | `connect-src` | `https://api-gateway.umami.dev` |
| CSS 样式 | `style-src` | `https://fonts.googleapis.com` |
| 字体文件 | `font-src` | `https://fonts.gstatic.com` |
| 图片 | `img-src` | `https://example.com` |
| 音频/视频 | `media-src` | `https://storage.example.com` |
| iframe 框架 | `frame-src` | `https://tally.so` |

### 步骤 3: 更新 CSP 配置
需要同时更新两个文件：

#### A. 更新 `next.config.mjs`
```javascript
// 在 script-src 中添加新域名
script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cloud.umami.is https://pa.uniscribe.co https://www.googletagmanager.com https://www.google-analytics.com https://js.sentry-cdn.com https://www.clarity.ms https://NEW_DOMAIN_HERE

// 在 connect-src 中添加新域名
connect-src 'self' https://api.uniscribe.co https://api-surge.uniscribe.co https://bgcrcrsosqswpseimvvx.supabase.co https://cloud.umami.is https://pa.uniscribe.co https://www.google-analytics.com https://o4508829103095808.ingest.us.sentry.io https://api-gateway.umami.dev https://www.clarity.ms https://NEW_API_DOMAIN_HERE
```

#### B. 更新 `src/middleware.js`
在两个地方更新相同的 CSP 字符串：
- `response.headers.set("Content-Security-Policy", ...)`
- `newResponse.headers.set("Content-Security-Policy", ...)`

## 📋 当前已允许的域名列表

### Script Sources (`script-src`)
- `'self'` - 同源脚本
- `'unsafe-inline'` - 内联脚本 (谨慎使用)
- `'unsafe-eval'` - eval() 函数 (谨慎使用)
- `https://cloud.umami.is` - Umami 分析
- `https://pa.uniscribe.co` - Plausible 分析
- `https://www.googletagmanager.com` - Google Tag Manager
- `https://www.google-analytics.com` - Google Analytics
- `https://js.sentry-cdn.com` - Sentry 错误监控
- `https://www.clarity.ms` - Microsoft Clarity
- `https://tally.so` - Tally 表单脚本

### Connect Sources (`connect-src`)
- `'self'` - 同源请求
- `https://api.uniscribe.co` - 主 API
- `https://api-surge.uniscribe.co` - 备用 API
- `https://bgcrcrsosqswpseimvvx.supabase.co` - Supabase
- `https://cloud.umami.is` - Umami 分析
- `https://pa.uniscribe.co` - Plausible 分析
- `https://www.google-analytics.com` - Google Analytics
- `https://o4508829103095808.ingest.us.sentry.io` - Sentry
- `https://api-gateway.umami.dev` - Umami API
- `https://*.clarity.ms` - Microsoft Clarity (通配符)
- `https://tally.so` - Tally 表单服务
- `https://shiyin.bf922d70183b00570ae0ab7e483fcf84.r2.cloudflarestorage.com` - Cloudflare R2 存储

### Media Sources (`media-src`)
- `'self'` - 同源媒体文件
- `data:` - Data URLs
- `blob:` - Blob URLs
- `https://shiyin.bf922d70183b00570ae0ab7e483fcf84.r2.cloudflarestorage.com` - Cloudflare R2 音频文件

### Frame Sources (`frame-src`)
- `'self'` - 同源 iframe
- `https://tally.so` - Tally 表单弹窗
- `https://www.youtube.com` - YouTube 视频嵌入
- `https://youtube.com` - YouTube 视频嵌入（无 www）

## 🔍 调试技巧

### 1. 使用 CSP Report-Only 模式
在开发环境中，可以使用 `Content-Security-Policy-Report-Only` 头部来测试 CSP 而不阻止资源：

```javascript
// 临时调试用，不要在生产环境使用
"Content-Security-Policy-Report-Only": "your-csp-policy-here"
```

### 2. 逐步收紧策略
1. 先使用宽松的策略确保功能正常
2. 逐步移除不必要的权限
3. 测试每个变更的影响

### 3. 使用在线 CSP 工具
- [CSP Evaluator](https://csp-evaluator.withgoogle.com/)
- [CSP Validator](https://cspvalidator.org/)

## ⚠️ 安全注意事项

### 避免使用的不安全指令
- `'unsafe-inline'` - 允许内联脚本/样式
- `'unsafe-eval'` - 允许 eval() 函数
- `*` - 允许所有域名

### 最佳实践
1. **最小权限原则**: 只允许必要的域名和资源
2. **定期审查**: 定期检查 CSP 配置是否还需要所有域名
3. **监控日志**: 关注 CSP 违规报告
4. **测试覆盖**: 确保所有功能在 CSP 限制下正常工作

## 🚀 添加新的第三方服务

当需要添加新的第三方服务时：

1. **识别所需权限**
   - 脚本加载: 添加到 `script-src`
   - API 调用: 添加到 `connect-src`
   - 样式加载: 添加到 `style-src`
   - 字体加载: 添加到 `font-src`

2. **选择域名配置方式**

   **方式 1: 具体域名（推荐）**
   ```javascript
   // 在 src/config/security.js 中
   connectSrc: [
     // ... 现有域名
     "https://www.clarity.ms",
     "https://i.clarity.ms"
   ]
   ```

   **方式 2: 通配符域名（谨慎使用）**
   ```javascript
   // 在 src/config/security.js 中
   connectSrc: [
     // ... 现有域名
     "https://*.clarity.ms"  // 允许所有 clarity.ms 子域名
   ]
   ```

3. **通配符使用指南**

   **✅ 适合使用通配符的情况：**
   - 信任的大型服务商（如 Microsoft、Google）
   - 服务有多个不可预知的子域名
   - 官方文档建议使用通配符

   **❌ 不建议使用通配符的情况：**
   - 不完全信任的第三方服务
   - 只有少数几个已知子域名
   - 安全要求极高的环境

4. **更新配置文件**
   - 更新 `src/config/security.js`（推荐，统一管理）
   - 或者更新 `next.config.mjs` 和 `src/middleware.js`（旧方式）

5. **测试验证**
   - 运行 `node security-check.js`
   - 在浏览器中测试功能
   - 检查控制台是否有 CSP 错误

## 📞 紧急处理

如果 CSP 导致生产环境功能异常：

1. **临时禁用 CSP** (紧急情况下)
   ```javascript
   // 注释掉 CSP 头部配置
   // response.headers.set("Content-Security-Policy", ...)
   ```

2. **快速修复**
   - 添加被阻止的域名到 CSP
   - 重新部署

3. **验证修复**
   - 检查功能是否恢复正常
   - 确认安全性没有降低

---

**记住**: CSP 是一个强大的安全工具，但需要仔细配置。当遇到问题时，不要简单地禁用它，而是找出根本原因并正确配置。
