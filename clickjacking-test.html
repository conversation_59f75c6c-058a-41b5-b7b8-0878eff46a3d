<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点击劫持漏洞测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .iframe-container {
            border: 2px solid #e74c3c;
            padding: 10px;
            margin: 10px 0;
            background-color: #ffeaea;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .instructions {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #495057;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 UniScribe 点击劫持漏洞修复验证</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>此页面用于验证 UniScribe 网站是否已成功修复点击劫持（Clickjacking）漏洞。</p>
            <ol>
                <li>如果下方的 iframe 显示错误信息或空白页面，说明修复成功</li>
                <li>如果 iframe 能正常显示登录页面，说明仍存在漏洞</li>
                <li>检查浏览器开发者工具的控制台是否有相关错误信息</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🎯 测试 1: 登录页面 iframe 嵌入测试</h2>
            <p>尝试在 iframe 中加载 UniScribe 登录页面：</p>
            
            <div class="iframe-container">
                <p><strong>⚠️ 如果修复成功，下方应该显示错误或空白页面</strong></p>
                <iframe 
                    src="https://www.uniscribe.co/auth/signin" 
                    title="UniScribe 登录页面测试"
                    onload="checkIframeLoad(this, 'signin-status')"
                    onerror="showError('signin-status')">
                </iframe>
            </div>
            
            <div id="signin-status" class="status info">
                📊 状态：正在加载...
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 测试 2: 注册页面 iframe 嵌入测试</h2>
            <p>尝试在 iframe 中加载 UniScribe 注册页面：</p>
            
            <div class="iframe-container">
                <p><strong>⚠️ 如果修复成功，下方应该显示错误或空白页面</strong></p>
                <iframe 
                    src="https://www.uniscribe.co/auth/signup" 
                    title="UniScribe 注册页面测试"
                    onload="checkIframeLoad(this, 'signup-status')"
                    onerror="showError('signup-status')">
                </iframe>
            </div>
            
            <div id="signup-status" class="status info">
                📊 状态：正在加载...
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 测试 3: 主页 iframe 嵌入测试</h2>
            <p>尝试在 iframe 中加载 UniScribe 主页：</p>
            
            <div class="iframe-container">
                <p><strong>⚠️ 如果修复成功，下方应该显示错误或空白页面</strong></p>
                <iframe 
                    src="https://www.uniscribe.co/" 
                    title="UniScribe 主页测试"
                    onload="checkIframeLoad(this, 'home-status')"
                    onerror="showError('home-status')">
                </iframe>
            </div>
            
            <div id="home-status" class="status info">
                📊 状态：正在加载...
            </div>
        </div>

        <div class="test-section">
            <h2>📊 测试结果总结</h2>
            <div id="overall-status" class="status info">
                📊 总体状态：测试进行中...
            </div>
            
            <div class="instructions">
                <h3>🔍 如何判断修复是否成功</h3>
                <ul>
                    <li><strong>✅ 修复成功</strong>：iframe 显示空白页面或错误信息</li>
                    <li><strong>❌ 仍有漏洞</strong>：iframe 能正常显示网站内容</li>
                    <li><strong>🔧 检查方法</strong>：打开浏览器开发者工具，查看控制台是否有 "Refused to display" 相关错误</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            signin: null,
            signup: null,
            home: null
        };

        function checkIframeLoad(iframe, statusId) {
            try {
                // 尝试访问 iframe 内容
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                if (!iframeDoc || iframeDoc.body.innerHTML.trim() === '') {
                    showSuccess(statusId, '修复成功 - iframe 被阻止加载');
                    updateTestResult(statusId, true);
                } else {
                    showError(statusId, '⚠️ 可能存在漏洞 - iframe 成功加载内容');
                    updateTestResult(statusId, false);
                }
            } catch (error) {
                // 如果访问被拒绝，说明修复成功
                showSuccess(statusId, '✅ 修复成功 - 跨域访问被阻止: ' + error.message);
                updateTestResult(statusId, true);
            }
            
            updateOverallStatus();
        }

        function showSuccess(statusId, message) {
            const statusEl = document.getElementById(statusId);
            statusEl.className = 'status success';
            statusEl.textContent = '✅ ' + message;
        }

        function showError(statusId, message = '❌ 可能存在漏洞 - iframe 加载失败但原因不明') {
            const statusEl = document.getElementById(statusId);
            statusEl.className = 'status error';
            statusEl.textContent = message;
            updateTestResult(statusId, false);
            updateOverallStatus();
        }

        function updateTestResult(statusId, isSecure) {
            if (statusId.includes('signin')) testResults.signin = isSecure;
            if (statusId.includes('signup')) testResults.signup = isSecure;
            if (statusId.includes('home')) testResults.home = isSecure;
        }

        function updateOverallStatus() {
            const results = Object.values(testResults);
            const completedTests = results.filter(r => r !== null).length;
            const secureTests = results.filter(r => r === true).length;
            
            const overallEl = document.getElementById('overall-status');
            
            if (completedTests === 0) {
                overallEl.className = 'status info';
                overallEl.textContent = '📊 总体状态：测试进行中...';
            } else if (completedTests < 3) {
                overallEl.className = 'status warning';
                overallEl.textContent = `📊 总体状态：已完成 ${completedTests}/3 项测试，${secureTests} 项安全`;
            } else {
                if (secureTests === 3) {
                    overallEl.className = 'status success';
                    overallEl.textContent = '🎉 总体状态：所有测试通过，点击劫持漏洞已成功修复！';
                } else {
                    overallEl.className = 'status error';
                    overallEl.textContent = `⚠️ 总体状态：${secureTests}/3 项测试通过，仍可能存在安全风险`;
                }
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('🔒 点击劫持漏洞测试页面已加载');
            console.log('📋 请查看页面中的 iframe 是否被成功阻止');
            
            // 设置超时检查
            setTimeout(function() {
                updateOverallStatus();
            }, 5000);
        });
    </script>
</body>
</html>
