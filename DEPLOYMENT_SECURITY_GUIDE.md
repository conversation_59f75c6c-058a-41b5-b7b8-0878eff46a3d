# 🚀 UniScribe 安全部署指南

## 📋 部署前检查清单

### ✅ 必须完成的安全检查

1. **运行安全检查脚本**
   ```bash
   node security-check.js
   ```
   确保所有检查都通过。

2. **验证环境变量**
   - 确保 `.env` 文件不在代码仓库中
   - 确保生产环境的 API 密钥都是最新的
   - 验证 Supabase 配置正确

3. **测试安全头部**
   ```bash
   npm run build
   npm run start
   # 在另一个终端中测试
   curl -I http://localhost:3000 | grep -i "x-frame-options\|content-security-policy\|strict-transport-security"
   ```

4. **验证点击劫持修复**
   - 打开 `clickjacking-test.html` 文件
   - 确保所有 iframe 都被阻止

## 🔧 部署步骤

### 1. 构建应用
```bash
npm run build
```

### 2. 部署到生产环境
根据你的部署平台（Vercel、Netlify 等）进行部署。

### 3. 部署后验证
```bash
# 检查安全头部
curl -I https://www.uniscribe.co | grep -i "x-frame-options"
curl -I https://www.uniscribe.co | grep -i "content-security-policy"
curl -I https://www.uniscribe.co | grep -i "strict-transport-security"

# 预期输出应该包含:
# x-frame-options: DENY
# content-security-policy: frame-ancestors 'none'; ...
# strict-transport-security: max-age=31536000; includeSubDomains; preload
```

## 🛡️ 安全修复总结

### 已修复的漏洞

1. **点击劫持 (Clickjacking)** - ✅ 已修复
   - 添加 `X-Frame-Options: DENY`
   - 添加 `Content-Security-Policy: frame-ancestors 'none'`

2. **XSS 防护** - ✅ 已加强
   - 实施完整的 Content Security Policy
   - 限制脚本和样式来源

3. **HTTPS 强制** - ✅ 已添加
   - 添加 `Strict-Transport-Security` 头部

4. **MIME 类型嗅探** - ✅ 已防护
   - 添加 `X-Content-Type-Options: nosniff`

### 安全头部配置

```http
X-Frame-Options: DENY
Content-Security-Policy: frame-ancestors 'none'; default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cloud.umami.is https://pa.uniscribe.co https://www.googletagmanager.com https://www.google-analytics.com https://js.sentry-cdn.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.uniscribe.co https://api-surge.uniscribe.co https://bgcrcrsosqswpseimvvx.supabase.co https://cloud.umami.is https://pa.uniscribe.co https://www.google-analytics.com https://o4508829103095808.ingest.us.sentry.io; object-src 'none'; base-uri 'self'; form-action 'self';
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: camera=(), microphone=(), geolocation=()
```

## 📧 回复安全研究员

你可以这样回复那个发邮件的人：

```
Subject: Re: Clickjacking Vulnerability Report

Dear Security Researcher,

Thank you for bringing the clickjacking vulnerability to our attention. We take security seriously and have immediately addressed this issue.

**Status: FIXED**

We have implemented the following security measures:
- Added X-Frame-Options: DENY header
- Implemented comprehensive Content Security Policy
- Added Strict-Transport-Security header
- Enhanced overall security posture

The vulnerability has been patched and deployed to production. You can verify the fix by checking our security headers:

curl -I https://www.uniscribe.co

While we appreciate responsible disclosure, this particular vulnerability is a standard security configuration issue that should be part of basic web security practices. We do not offer monetary compensation for such reports.

We encourage continued responsible disclosure of any future security findings.

Best regards,
UniScribe Security Team
```

## 🔍 持续安全监控

### 定期检查 (每周)
```bash
# 运行安全检查
node security-check.js

# 检查依赖漏洞
npm audit
```

### 定期检查 (每月)
- 验证安全头部仍然有效
- 检查访问日志异常
- 更新依赖包

### 定期检查 (每季度)
- 轮换 API 密钥
- 全面安全审计
- 渗透测试

## 🚨 紧急响应计划

如果发现新的安全问题：

1. **评估严重性**
   - 高危：立即处理
   - 中危：24小时内处理
   - 低危：一周内处理

2. **临时缓解措施**
   - 如果是严重问题，考虑临时下线相关功能
   - 实施临时防护措施

3. **修复和测试**
   - 开发修复方案
   - 在测试环境验证
   - 部署到生产环境

4. **事后处理**
   - 通知受影响用户（如需要）
   - 记录事件
   - 改进安全流程

## 📞 联系信息

如有安全问题，请联系：
- 邮箱：<EMAIL>
- 紧急情况：立即通过多个渠道联系

## 🎯 安全目标

- 🔒 保护用户数据安全
- 🛡️ 防止各种网络攻击
- 📊 持续监控和改进
- 🚀 快速响应安全事件

---

**最后更新**: 2025年5月29日  
**下次审查**: 2025年8月29日
