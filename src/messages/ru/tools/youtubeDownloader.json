{"meta": {"title": "Бесплатный загрузчик видео с YouTube | Инструменты UniScribe", "description": "Скачивайте видео с YouTube в HD качестве бесплатно. Конвертируйте видео с YouTube в MP4, MP3. Регистрация не требуется, быстрые и безопасные загрузки.", "ogImageAlt": "UniScribe Загрузчик видео с YouTube"}, "loading": "Загрузка...", "hero": {"title": "Бесплатный загрузчик видео с YouTube", "subtitle": "Скачивайте видео с YouTube высокого качества мгновенно"}, "features": {"free": {"title": "Совершенно бесплатно", "description": "Без подписок, без скрытых платежей"}, "noRegistration": {"title": "Без регистрации", "description": "Используйте напрямую без создания учетной записи"}, "quality": {"title": "HD качество", "description": "Поддерживает загрузку разрешения до 4K."}}, "buttons": {"search": "Поиск", "searching": "Поиск...", "tryAnother": "Попробуйте другое видео"}, "copyright": {"warning": "⚠️ Использование видео, которым вы не владеете, может нарушать законы об авторском праве.", "confirmation": "Я подтверждаю, что это мой собственный оригинальный контент или у меня есть разрешение на его загрузку."}, "formats": {"videoWithAudio": {"title": "Видео с аудио"}, "videoOnly": {"title": "Только видео", "description": "📹 Форматы только видео не содержат аудио. Это высококачественные видеофайлы без звука. Чтобы получить видео в высоком качестве с аудио, скачайте отдельно видео и аудио, а затем объедините их с помощью видеоредактора, например Final Cut Pro, Premiere или CapCut."}, "audioOnly": {"title": "Только аудио", "description": "🎵 Форматы только аудио не содержат видео. Идеально подходят для музыки, подкастов или случаев, когда нужен только звуковой файл. Их можно объединить с видеофайлом в видеоредакторе для создания пользовательского видео высокого качества."}}, "faq": {"what": {"question": "Что такое YouTube Video Downloader?", "answer": "YouTube Video Downloader — это бесплатный инструмент, который позволяет загружать видео с YouTube в различных форматах и качествах, включая MP4, WebM и форматы только для аудио."}, "legal": {"question": "Законно ли скачивать видео с YouTube?", "answer": "Законность загрузки видео с YouTube зависит от конкретной ситуации. Законно загружать собственный контент или контент, на загрузку которого у вас есть разрешение. Однако загрузка защищенного авторским правом контента без разрешения может нарушать закон."}, "quality": {"question": "Какие качества видео поддерживаются?", "answer": "Мы поддерживаем все стандартные разрешения YouTube от 360p до 4K (2160p). Доступные качества зависят от качества загрузки оригинального видео."}, "audio": {"question": "Могу ли я скачать только аудио?", "answer": "Да, вы можете выбрать загрузку в формате только аудио (MP3). Это особенно полезно для музыкальных видео или когда вам нужна только аудиочасть."}, "limit": {"question": "Существует ли ограничение на загрузку?", "answer": "В настоящее время мы не устанавливаем никаких ограничений на загрузку для бесплатных пользователей. Однако мы рекомендуем разумное использование для поддержания качества обслуживания для всех."}, "account": {"question": "Мне нужно создать учетную запись?", "answer": "Нет. Наш загрузчик полностью бесплатен для использования без регистрации. Вы можете начать загрузку немедленно, вставив ссылку."}, "browsers": {"question": "Какие браузеры поддерживаются?", "answer": "Мы поддерживаем все основные браузеры, включая Chrome, Firefox, Safari, Edge и Opera. Мы рекомендуем использовать последнюю версию вашего браузера для наилучшего опыта."}, "unavailable": {"question": "Почему я не могу скачать некоторые видео?", "answer": "Некоторые видео могут быть недоступны для загрузки из-за ограничений авторского права, региональных ограничений или настроек конфиденциальности видео. Кроме того, видео, которые были удалены или установлены как частные, не могут быть загружены."}}, "noFormats": {"title": "Нет доступной загрузки", "description": "Это видео не может быть загружено. Оно может быть прямой трансляцией, частным видео или иметь ограничения на загрузку. Если у вас есть вопросы, пожалуйста, свяжитесь с нами: <EMAIL>"}}