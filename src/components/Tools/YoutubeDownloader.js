"use client";

import { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import { Loader2, CircleDollarSign, UserX, DownloadCloud } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";

import { toolsService } from "@/services/api/toolsService";
import { FeaturesGrid } from "@/components/Common/FeaturesGrid";
import { useTranslations } from "next-intl";
import isValidYouTubeUrl from "@/lib/youtube";
import { safeOpenUrl } from "@/lib/browserUtils";
import { trackEvent } from "@/lib/analytics";

// 将文件大小格式化函数移到组件外部
const formatFileSize = (fileSize) => {
  if (fileSize < 1024) return `${fileSize} B`;
  if (fileSize < 1024 * 1024) return `${(fileSize / 1024).toFixed(2)} KB`;
  if (fileSize < 1024 * 1024 * 1024)
    return `${(fileSize / (1024 * 1024)).toFixed(2)} MB`;
  return `${(fileSize / (1024 * 1024 * 1024)).toFixed(2)} GB`;
};

// 格式过滤函数 - 精简格式选择
const filterFormats = (formats) => {
  const filtered = {
    video_with_audio: [],
    video_only: [],
    audio: []
  };

  // Video with Audio - 通常是 mp4，保持不变
  filtered.video_with_audio = formats.video_with_audio || [];

  // Video Only - 优先保留 mp4，相同分辨率只保留一个
  if (formats.video_only && formats.video_only.length > 0) {
    const videoOnlyMap = new Map();

    formats.video_only.forEach(format => {
      // 提取分辨率和帧率信息作为 key
      const resolutionKey = `${format.height}p${format.fps || ''}`;

      if (format.ext === 'mp4') {
        // mp4 格式优先，直接覆盖
        videoOnlyMap.set(resolutionKey, format);
      } else if (!videoOnlyMap.has(resolutionKey)) {
        // 如果该分辨率还没有格式，且不是 mp4，则跳过（按照需求不展示 webm）
        // 这里我们不添加非 mp4 格式
      }
    });

    filtered.video_only = Array.from(videoOnlyMap.values());
  }

  // Audio - 只保留 m4a 格式
  if (formats.audio && formats.audio.length > 0) {
    filtered.audio = formats.audio.filter(format => format.ext === 'm4a');
  }

  return filtered;
};

const YoutubeDownloader = () => {
  const t = useTranslations("tools.youtubeDownloader");
  const searchParams = useSearchParams();
  const [url, setUrl] = useState("");
  const [videoData, setVideoData] = useState(null);
  const [confirmed, setConfirmed] = useState(true);
  const [selectedFormat, setSelectedFormat] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [initialUrlProcessed, setInitialUrlProcessed] = useState(false);

  const features = [
    {
      icon: CircleDollarSign,
      title: t("features.free.title"),
      description: t("features.free.description"),
    },
    {
      icon: UserX,
      title: t("features.noRegistration.title"),
      description: t("features.noRegistration.description"),
    },
    {
      icon: DownloadCloud,
      title: t("features.quality.title"),
      description: t("features.quality.description"),
    },
  ];

  // 统一的搜索处理函数
  const handleSearch = useCallback(
    async (searchUrl = url) => {
      if (!searchUrl) return;

      setIsLoading(true);
      setError(null);

      try {
        const validation = isValidYouTubeUrl(searchUrl);
        if (!validation.isValid) {
          throw new Error(validation.reason);
        }
        // Use the extracted URL if available
        const urlToUse = validation.extractedUrl || searchUrl;

        // 记录搜索事件
        trackEvent("youtube_search", { url: urlToUse });

        const data = await toolsService.getYoutubeInfo(urlToUse);

        // 应用格式过滤
        const filteredFormats = filterFormats(data.formats);
        const filteredData = {
          ...data,
          formats: filteredFormats
        };

        setVideoData(filteredData);
        // 自动选择第一个格式 - 优先级：video_with_audio > video_only > audio
        let autoSelectedFormat = null;
        if (filteredData.formats.video_with_audio.length > 0) {
          autoSelectedFormat = filteredData.formats.video_with_audio[0];
        } else if (filteredData.formats.video_only.length > 0) {
          autoSelectedFormat = filteredData.formats.video_only[0];
        } else if (filteredData.formats.audio.length > 0) {
          autoSelectedFormat = filteredData.formats.audio[0];
        }
        setSelectedFormat(autoSelectedFormat);

        // 记录搜索成功事件
        trackEvent("youtube_search_success", {
          url: urlToUse,
          title: data.title,
        });
      } catch (error) {
        setError(error?.data?.message || error?.message);
        setVideoData(null);

        // 记录搜索失败事件
        trackEvent("youtube_search_error", {
          url: searchUrl,
          error: error?.data?.message || error?.message
        });
      } finally {
        setIsLoading(false);
      }
    },
    [url]
  );

  // 处理 URL 参数
  useEffect(() => {
    const youtubeUrl = searchParams.get("url");
    if (youtubeUrl && !initialUrlProcessed) {
      setUrl(youtubeUrl);
      handleSearch(youtubeUrl);
      setInitialUrlProcessed(true);
    }
  }, [searchParams, handleSearch, initialUrlProcessed]);

  // 处理输入框变化
  const handleUrlChange = (e) => {
    if (e.nativeEvent.inputType === "insertFromPaste") return;

    const newValue = e.target.value;
    setUrl(newValue);

    if (!newValue) {
      setVideoData(null);
      setError(null);
      setIsLoading(false);
      setConfirmed(true);
      setSelectedFormat(null);
    }
  };

  // 处理粘贴
  const handlePaste = async (e) => {
    const pastedUrl = e.clipboardData.getData("text");
    if (!pastedUrl) return;

    setUrl(pastedUrl);
    await handleSearch(pastedUrl);
  };

  // 完整的重置函数
  const handleReset = () => {
    // 记录重置事件
    if (videoData) {
      trackEvent("youtube_reset", {
        title: videoData.title
      });
    }

    setUrl("");
    setVideoData(null);
    setConfirmed(true);
    setSelectedFormat(null);
    setError(null);
    setIsLoading(false);
  };

  // 处理格式选择
  const handleFormatChange = (formatId) => {
    const format = [
      ...videoData.formats.video_with_audio,
      ...videoData.formats.video_only,
      ...videoData.formats.audio,
    ].find((f) => f.formatId === formatId);
    setSelectedFormat(format);

    // 记录格式选择事件
    if (format) {
      let formatType = "unknown";
      if (videoData.formats.video_with_audio.some(f => f.formatId === formatId)) {
        formatType = "video_with_audio";
      } else if (videoData.formats.video_only.some(f => f.formatId === formatId)) {
        formatType = "video_only";
      } else if (videoData.formats.audio.some(f => f.formatId === formatId)) {
        formatType = "audio";
      }

      trackEvent("youtube_format_selected", {
        formatType: formatType,
        label: format.label,
        ext: format.ext,
      });
    }
  };

  return (
    <div className="max-w-3xl mx-auto space-y-6 px-4 sm:px-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-custom-bg">
          {t("hero.title")}
        </h1>
        <h2 className="text-lg sm:text-xl font-semibold">
          {t("hero.subtitle")}
        </h2>
      </div>

      <FeaturesGrid features={features} />

      {/* URL Input */}
      <div className="flex flex-col sm:flex-row gap-3 pt-8">
        <Input
          type="text"
          placeholder="https://www.youtube.com/watch?v=7k1ehaE0bdU"
          value={url}
          onChange={handleUrlChange}
          onPaste={handlePaste}
          disabled={isLoading}
          className="h-12 text-base sm:text-lg border-custom-bg focus:ring-custom-bg focus:border-custom-bg hover:border-custom-bg hover:ring-1 hover:ring-custom-bg transition-all duration-200"
        />
        <Button
          onClick={() => handleSearch()}
          disabled={!url || isLoading}
          className="h-12 px-8 text-base sm:text-lg bg-custom-bg hover:bg-custom-bg-600-hover whitespace-nowrap"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("buttons.searching")}
            </>
          ) : (
            t("buttons.search")
          )}
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="text-red-500 text-sm text-center" role="alert">
          {error}
        </div>
      )}

      {/* Video Data */}
      {videoData && (
        <div className="space-y-6">
          {/* Video Preview */}
          <div className="rounded-lg overflow-hidden max-w-[300px] sm:max-w-[400px] mx-auto shadow-lg">
            <img
              src={videoData.thumbnailUrl}
              alt={videoData.title}
              className="w-full h-auto object-cover"
            />
          </div>

          {/* Video Title */}
          <div className="text-center">
            <h3 className="text-base sm:text-lg font-medium line-clamp-2">
              {videoData.title}
            </h3>
          </div>

          {/* Check if no formats available */}
          {videoData.formats.video_with_audio.length === 0 &&
            videoData.formats.video_only.length === 0 &&
            videoData.formats.audio.length === 0 ? (
            /* No Download Available Message */
            <div className="text-center space-y-4 py-8">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div className="flex flex-col items-center space-y-3">
                  <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="text-center">
                    <h4 className="text-lg font-semibold text-yellow-800 mb-2">
                      {t("noFormats.title")}
                    </h4>
                    <p className="text-yellow-700 text-sm sm:text-base max-w-md">
                      {t("noFormats.description")}
                    </p>
                  </div>
                </div>
              </div>
              <Button
                onClick={handleReset}
                variant="outline"
                className="w-full max-w-xs h-11 sm:h-12 text-base sm:text-lg"
              >
                {t("buttons.tryAnother")}
              </Button>
            </div>
          ) : (
            /* Download Options */
            <div className="space-y-6">
              {/* Video with Audio */}
              {videoData.formats.video_with_audio.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-lg font-semibold text-gray-800">{t("formats.videoWithAudio.title")}</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {videoData.formats.video_with_audio.map((format) => (
                      <div
                        key={format.formatId}
                        onClick={() => handleFormatChange(format.formatId)}
                        className={`
                        relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200
                        ${selectedFormat?.formatId === format.formatId
                            ? 'border-custom-bg bg-custom-bg/5 shadow-md'
                            : 'border-gray-200 hover:border-custom-bg/50 hover:shadow-sm'
                          }
                      `}
                      >
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-900">{format.label}</span>
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              {format.ext.toUpperCase()}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>Size: {formatFileSize(format.fileSize)}</p>
                            {format.vcodec && <p>Video: {format.vcodec}</p>}
                            {format.acodec && <p>Audio: {format.acodec}</p>}
                          </div>
                        </div>
                        {selectedFormat?.formatId === format.formatId && (
                          <div className="absolute top-2 right-2">
                            <div className="w-5 h-5 bg-custom-bg rounded-full flex items-center justify-center">
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Video Only */}
              {videoData.formats.video_only.length > 0 && (
                <div className="space-y-3">
                  <div className="space-y-2">
                    <h4 className="text-lg font-semibold text-gray-800">{t("formats.videoOnly.title")}</h4>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-sm text-blue-800">
                        {t("formats.videoOnly.description")}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {videoData.formats.video_only.map((format) => (
                      <div
                        key={format.formatId}
                        onClick={() => handleFormatChange(format.formatId)}
                        className={`
                        relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200
                        ${selectedFormat?.formatId === format.formatId
                            ? 'border-custom-bg bg-custom-bg/5 shadow-md'
                            : 'border-gray-200 hover:border-custom-bg/50 hover:shadow-sm'
                          }
                      `}
                      >
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-900">{format.label}</span>
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                              {format.ext.toUpperCase()}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>Size: {formatFileSize(format.fileSize)}</p>
                            {format.vcodec && <p>Video: {format.vcodec}</p>}
                          </div>
                        </div>
                        {selectedFormat?.formatId === format.formatId && (
                          <div className="absolute top-2 right-2">
                            <div className="w-5 h-5 bg-custom-bg rounded-full flex items-center justify-center">
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Audio Only */}
              {videoData.formats.audio.length > 0 && (
                <div className="space-y-3">
                  <div className="space-y-2">
                    <h4 className="text-lg font-semibold text-gray-800">{t("formats.audioOnly.title")}</h4>
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                      <p className="text-sm text-orange-800">
                        {t("formats.audioOnly.description")}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {videoData.formats.audio.map((format) => (
                      <div
                        key={format.formatId}
                        onClick={() => handleFormatChange(format.formatId)}
                        className={`
                        relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200
                        ${selectedFormat?.formatId === format.formatId
                            ? 'border-custom-bg bg-custom-bg/5 shadow-md'
                            : 'border-gray-200 hover:border-custom-bg/50 hover:shadow-sm'
                          }
                      `}
                      >
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-900">{format.label}</span>
                            <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                              {format.ext.toUpperCase()}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>Size: {formatFileSize(format.fileSize)}</p>
                            {format.acodec && <p>Audio: {format.acodec}</p>}
                          </div>
                        </div>
                        {selectedFormat?.formatId === format.formatId && (
                          <div className="absolute top-2 right-2">
                            <div className="w-5 h-5 bg-custom-bg rounded-full flex items-center justify-center">
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Copyright Warning - Moved closer to download button */}
              <div className="border border-red-300 rounded-lg p-4">
                <div className="text-center space-y-3">
                  <p className="text-red-500 text-base sm:text-lg flex items-center justify-center">
                    {t("copyright.warning")}
                  </p>
                  <div className="flex items-center justify-center gap-2 py-2">
                    <Checkbox
                      id="confirm"
                      checked={confirmed}
                      onCheckedChange={setConfirmed}
                      className="h-5 w-5 text-custom-bg"
                    />
                    <label
                      htmlFor="confirm"
                      className="text-sm sm:text-base text-gray-700 cursor-pointer"
                    >
                      {t("copyright.confirmation")}
                    </label>
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-3">
                <div className="relative">
                  <Button
                    disabled={!confirmed || !selectedFormat}
                    className={`w-full h-11 sm:h-12 text-base sm:text-lg bg-custom-bg hover:bg-custom-bg-600-hover ${!confirmed && selectedFormat ? "opacity-40 cursor-not-allowed" : "disabled:opacity-50"
                      }`}
                    onClick={() => {
                      if (selectedFormat) {
                        // 记录下载事件
                        let formatType = "unknown";
                        if (videoData.formats.video_with_audio.some(f => f.formatId === selectedFormat.formatId)) {
                          formatType = "video_with_audio";
                        } else if (videoData.formats.video_only.some(f => f.formatId === selectedFormat.formatId)) {
                          formatType = "video_only";
                        } else if (videoData.formats.audio.some(f => f.formatId === selectedFormat.formatId)) {
                          formatType = "audio";
                        }

                        trackEvent("youtube_download", {
                          title: videoData.title,
                          formatType: formatType,
                          label: selectedFormat.label,
                          ext: selectedFormat.ext,
                        });

                        safeOpenUrl(selectedFormat.url);
                      }
                    }}
                  >
                    Download {selectedFormat ? `(${selectedFormat.label})` : ""}
                  </Button>
                  {!confirmed && selectedFormat && (
                    <div className="absolute -top-8 left-0 right-0 text-center">
                      <div className="inline-block bg-red-50 text-red-500 text-sm py-1 px-3 rounded-t-md border border-red-200 shadow-sm animate-pulse">
                        ↓ {"Please confirm copyright notice to download"} ↓
                      </div>
                    </div>
                  )}
                </div>
                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="w-full h-11 sm:h-12 text-base sm:text-lg"
                >
                  Reset
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default YoutubeDownloader;
