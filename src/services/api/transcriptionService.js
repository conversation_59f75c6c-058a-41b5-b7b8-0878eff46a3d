import axiosInstance from "./axiosInstance";
import {
  GET_TRANSCRIPTIONS,
  GET_TRANSCRIPTION_BY_ID,
  EXPORT_TRANSCRIPTION,
  CREATE_TRANSCRIPTION_TASK,
  CREATE_YOUTUBE_TRANSCRIPTION_TASK,
  RENAME_TRANSCRIPTION,
  DELETE_TRANSCRIPTION,
  GET_TRANSCRIPTIONS_BY_PAGE,
  SEARCH_TRANSCRIPTIONS,
  UPDATE_LANGUAGE_CODE,
  GET_ANONYMOUS_LATEST_FILE,
  MIGRATE_ANONYMOUS_FILE,
  UPDATE_TRANSCRIPTION_TYPE,
  UNLOCK_TRANSCRIPTION,
  UPDATE_SEGMENT,
} from "@/constants/endpoints";

export const transcriptionService = {
  getTranscriptions: async (cursor = null) => {
    const params = cursor ? { cursor } : {};
    return await axiosInstance.get(GET_TRANSCRIPTIONS, { params });
  },

  getTranscriptionById: async (id) => {
    const response = await axiosInstance.get(GET_TRANSCRIPTION_BY_ID(id));
    return response;
  },

  exportTranscription: async (fileId, format, options = {}) => {
    const response = await axiosInstance.post(
      EXPORT_TRANSCRIPTION,
      {
        fileId,
        fileType: format,
        ...(typeof options.showSpeaker === "boolean"
          ? { showSpeakerName: options.showSpeaker }
          : {}),
        ...(typeof options.showTimestamp === "boolean"
          ? { showTimestamps: options.showTimestamp }
          : {}),
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        responseType: "blob", // 设置响应类型为blob
      }
    );
    return response;
  },

  doTranscription: async (transcriptionFileId) => {
    const response = await axiosInstance.post(CREATE_TRANSCRIPTION_TASK, {
      transcriptionFileId,
    });
    return response;
  },

  createYoutubeTranscriptionTask: async (
    url,
    title,
    duration,
    subtitleEnabled,
    languageCode
  ) => {
    const transcriptionType = subtitleEnabled ? "subtitle" : "transcript";
    const response = await axiosInstance.post(
      CREATE_YOUTUBE_TRANSCRIPTION_TASK,
      {
        url,
        title,
        duration,
        transcriptionType,
        languageCode,
      }
    );
    return response;
  },

  renameTranscription: async (id, filename) => {
    const response = await axiosInstance.patch(RENAME_TRANSCRIPTION(id), {
      filename,
    });
    return response;
  },

  updateLanguageCode: async (id, languageCode) => {
    const response = await axiosInstance.patch(UPDATE_LANGUAGE_CODE(id), {
      languageCode,
    });
    return response;
  },

  deleteTranscription: async (id) => {
    const response = await axiosInstance.delete(DELETE_TRANSCRIPTION(id));
    return response;
  },

  // 新增基于页码的分页接口
  getTranscriptionsByPage: async (page, pageSize) => {
    return await axiosInstance.get(GET_TRANSCRIPTIONS_BY_PAGE, {
      params: {
        page,
        pageSize,
      },
    });
  },

  searchTranscriptions: (keyword, page, pageSize) => {
    return axiosInstance.get(SEARCH_TRANSCRIPTIONS, {
      params: {
        keyword,
        page,
        pageSize,
      },
    });
  },

  getAnonymousLatestFile: async () => {
    const response = await axiosInstance.get(GET_ANONYMOUS_LATEST_FILE);
    return response;
  },

  migrateAnonymousFile: async (fileId) => {
    const response = await axiosInstance.post(MIGRATE_ANONYMOUS_FILE, {
      fileId,
    });
    return response;
  },

  updateTranscriptionType: async (id, transcriptionType) => {
    const response = await axiosInstance.patch(UPDATE_TRANSCRIPTION_TYPE(id), {
      transcriptionType,
    });
    return response;
  },

  unlockTranscription: async (id) => {
    const response = await axiosInstance.post(UNLOCK_TRANSCRIPTION(id));
    return response;
  },

  updateSegment: async (fileId, segmentId, text) => {
    const response = await axiosInstance.patch(
      UPDATE_SEGMENT(fileId, segmentId),
      {
        text,
      }
    );
    return response;
  },
};
