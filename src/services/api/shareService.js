import axiosInstance from "./axiosInstance";
import { SERVER_API_URL } from "@/config";
import {
  CREATE_SHARE,
  GET_SHARE_BY_CODE,
  EXPORT_SHARE_TRANSCRIPTION,
  SHARER_PLAN_CONFIG,
} from "@/constants/endpoints";

export const serverShareService = {
  // no auth
  getSharedTranscriptionByCode: async (shareCode) => {
    try {
      const response = await fetch(
        `${SERVER_API_URL}${GET_SHARE_BY_CODE(shareCode)}`,
        {
          headers: {
            "Content-Type": "application/json",
          },
          cache: "no-store",
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Server: Error fetching shared transcription", error);
      throw error;
    }
  },

  // no auth
  getSharerPlanConfig: async (shareCode) => {
    try {
      const response = await fetch(
        `${SERVER_API_URL}${SHARER_PLAN_CONFIG}?shareCode=${shareCode}`,
        {
          headers: {
            "Content-Type": "application/json",
          },
          cache: "no-store",
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Server: Error fetching plan config", error);
      throw error;
    }
  },
};

export const shareService = {
  createShare: async (transcriptionId) => {
    const response = await axiosInstance.post(CREATE_SHARE(transcriptionId), {
      transcriptionId,
    });
    return response;
  },

  exportShareTranscription: async (fileId, format, options = {}) => {
    const response = await axiosInstance.post(
      EXPORT_SHARE_TRANSCRIPTION,
      {
        fileId,
        fileType: format,
        ...(typeof options.showSpeaker === "boolean"
          ? { showSpeakerName: options.showSpeaker }
          : {}),
        ...(typeof options.showTimestamp === "boolean"
          ? { showTimestamps: options.showTimestamp }
          : {}),
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        responseType: "blob",
      }
    );
    return response;
  },
};
