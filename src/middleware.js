import createMiddleware from "next-intl/middleware";
import { locales, defaultLocale } from "@/config/i18n";
import { NextResponse } from "next/server";

const intlMiddleware = createMiddleware({
  locales,
  defaultLocale,
  localePrefix: "as-needed", // 只在非默认语言时添加前缀
  localeDetection: false, // 禁用语言检测
  alternateLinks: true, // 启用语言切换链接
});

export default function middleware(request) {
  // 先执行国际化中间件
  const response = intlMiddleware(request);

  // 添加安全头部，防止各种安全攻击
  if (response) {
    // 防止页面被嵌入 iframe
    response.headers.set("X-Frame-Options", "DENY");

    // 完整的 Content Security Policy
    response.headers.set("Content-Security-Policy",
      "frame-ancestors 'none'; default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cloud.umami.is https://pa.uniscribe.co https://www.googletagmanager.com https://www.google-analytics.com https://js.sentry-cdn.com https://www.clarity.ms; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.uniscribe.co https://api-surge.uniscribe.co https://bgcrcrsosqswpseimvvx.supabase.co https://cloud.umami.is https://pa.uniscribe.co https://www.google-analytics.com https://o4508829103095808.ingest.us.sentry.io https://api-gateway.umami.dev https://www.clarity.ms; object-src 'none'; base-uri 'self'; form-action 'self';"
    );

    // 强制 HTTPS
    response.headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");

    // 其他安全头部
    response.headers.set("X-Content-Type-Options", "nosniff");
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
    response.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()");

    return response;
  }

  // 如果没有响应，创建一个新的响应并添加安全头部
  const newResponse = NextResponse.next();
  newResponse.headers.set("X-Frame-Options", "DENY");
  newResponse.headers.set("Content-Security-Policy",
    "frame-ancestors 'none'; default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cloud.umami.is https://pa.uniscribe.co https://www.googletagmanager.com https://www.google-analytics.com https://js.sentry-cdn.com https://www.clarity.ms; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.uniscribe.co https://api-surge.uniscribe.co https://bgcrcrsosqswpseimvvx.supabase.co https://cloud.umami.is https://pa.uniscribe.co https://www.google-analytics.com https://o4508829103095808.ingest.us.sentry.io https://api-gateway.umami.dev https://www.clarity.ms; object-src 'none'; base-uri 'self'; form-action 'self';"
  );
  newResponse.headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
  newResponse.headers.set("X-Content-Type-Options", "nosniff");
  newResponse.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  newResponse.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()");

  return newResponse;
}

export const config = {
  // 匹配所有路径，除了:
  // - api 路由
  // - _next 系统文件
  // - _vercel 系统文件
  // - 带文件扩展名的文件
  matcher: [
    // 需要国际化的路径
    "/((?!api|_next|_vercel|.*\\..*).*)",
  ],
};
