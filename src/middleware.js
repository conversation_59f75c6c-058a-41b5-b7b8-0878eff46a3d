import createMiddleware from "next-intl/middleware";
import { locales, defaultLocale } from "@/config/i18n";
import { NextResponse } from "next/server";
import { setSecurityHeaders } from "@/config/security";

const intlMiddleware = createMiddleware({
  locales,
  defaultLocale,
  localePrefix: "as-needed", // 只在非默认语言时添加前缀
  localeDetection: false, // 禁用语言检测
  alternateLinks: true, // 启用语言切换链接
});

export default function middleware(request) {
  // 先执行国际化中间件
  const response = intlMiddleware(request);

  // 添加安全头部，防止各种安全攻击
  if (response) {
    setSecurityHeaders(response);
    return response;
  }

  // 如果没有响应，创建一个新的响应并添加安全头部
  const newResponse = NextResponse.next();
  setSecurityHeaders(newResponse);
  return newResponse;
}

export const config = {
  // 匹配所有路径，除了:
  // - api 路由
  // - _next 系统文件
  // - _vercel 系统文件
  // - 带文件扩展名的文件
  matcher: [
    // 需要国际化的路径
    "/((?!api|_next|_vercel|.*\\..*).*)",
  ],
};
