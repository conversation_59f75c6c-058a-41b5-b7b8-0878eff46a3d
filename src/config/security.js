/**
 * 🔒 安全配置统一管理
 *
 * 这个文件统一管理所有安全相关的配置，避免重复定义
 * 包括 CSP 域名、安全头部等配置
 *
 * 🚨 关于通配符域名的使用：
 * - 可以使用 *.example.com 来允许所有子域名
 * - 但建议谨慎使用，优先使用具体域名
 * - 通配符会降低安全性，因为允许了未知的子域名
 * - 对于信任的服务（如 Microsoft Clarity），可以考虑使用通配符
 *
 * 示例：
 * - 推荐: "https://www.clarity.ms", "https://i.clarity.ms"
 * - 可选: "https://*.clarity.ms" (如果确定信任整个域名)
 */

// CSP 允许的域名配置
export const CSP_DOMAINS = {
  // 脚本源域名
  scriptSrc: [
    "'self'",
    "'unsafe-inline'", // 注意：这个在生产环境中应该尽量避免
    "'unsafe-eval'",   // 注意：这个在生产环境中应该尽量避免
    "https://cloud.umami.is",
    "https://pa.uniscribe.co",
    "https://www.googletagmanager.com",
    "https://www.google-analytics.com",
    "https://js.sentry-cdn.com",
    "https://www.clarity.ms",
    "https://tally.so" // Tally 表单脚本
  ],

  // 样式源域名
  styleSrc: [
    "'self'",
    "'unsafe-inline'", // 对于样式来说相对安全
    "https://fonts.googleapis.com",
    "https://tally.so" // Tally 表单样式
  ],

  // 字体源域名
  fontSrc: [
    "'self'",
    "https://fonts.gstatic.com"
  ],

  // 图片源域名
  imgSrc: [
    "'self'",
    "data:",
    "https:",
    "blob:"
  ],

  // 媒体源域名 (音频、视频)
  mediaSrc: [
    "'self'",
    "data:",
    "blob:",
    "https://shiyin.bf922d70183b00570ae0ab7e483fcf84.r2.cloudflarestorage.com" // Cloudflare R2 音频文件
  ],

  // 框架源域名 (iframe)
  frameSrc: [
    "'self'",
    "https://tally.so", // Tally 表单弹窗
    "https://*.youtube.com", // YouTube 视频嵌入
  ],

  // 连接源域名 (API 请求等)
  connectSrc: [
    "'self'",
    "https://api.uniscribe.co",
    "https://api-surge.uniscribe.co",
    "https://bgcrcrsosqswpseimvvx.supabase.co",
    "https://cloud.umami.is",
    "https://pa.uniscribe.co",
    "https://www.google-analytics.com",
    "https://o4508829103095808.ingest.us.sentry.io",
    "https://api-gateway.umami.dev",
    // 使用通配符允许所有 Clarity 子域名
    "https://*.clarity.ms",
    "https://tally.so", // Tally 表单服务
    "https://shiyin.bf922d70183b00570ae0ab7e483fcf84.r2.cloudflarestorage.com" // Cloudflare R2 存储
  ]
};

// 生成完整的 CSP 策略字符串
export function generateCSPPolicy() {
  const policies = [
    "frame-ancestors 'none'",
    `default-src 'self'`,
    `script-src ${CSP_DOMAINS.scriptSrc.join(' ')}`,
    `style-src ${CSP_DOMAINS.styleSrc.join(' ')}`,
    `font-src ${CSP_DOMAINS.fontSrc.join(' ')}`,
    `img-src ${CSP_DOMAINS.imgSrc.join(' ')}`,
    `media-src ${CSP_DOMAINS.mediaSrc.join(' ')}`,
    `frame-src ${CSP_DOMAINS.frameSrc.join(' ')}`,
    `connect-src ${CSP_DOMAINS.connectSrc.join(' ')}`,
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ];

  return policies.join('; ') + ';';
}

// 其他安全头部配置
export const SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
};

// 为 Next.js headers() 函数生成配置
export function generateNextJSSecurityHeaders() {
  return [
    {
      key: 'X-Frame-Options',
      value: SECURITY_HEADERS['X-Frame-Options']
    },
    {
      key: 'Content-Security-Policy',
      value: generateCSPPolicy()
    },
    {
      key: 'Strict-Transport-Security',
      value: SECURITY_HEADERS['Strict-Transport-Security']
    },
    {
      key: 'X-Content-Type-Options',
      value: SECURITY_HEADERS['X-Content-Type-Options']
    },
    {
      key: 'Referrer-Policy',
      value: SECURITY_HEADERS['Referrer-Policy']
    },
    {
      key: 'Permissions-Policy',
      value: SECURITY_HEADERS['Permissions-Policy']
    }
  ];
}

// 为中间件设置安全头部的辅助函数
export function setSecurityHeaders(response) {
  // 设置 CSP
  response.headers.set('Content-Security-Policy', generateCSPPolicy());

  // 设置其他安全头部
  Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
}

// 添加新域名的辅助函数
export function addDomainToCSP(category, domain) {
  if (CSP_DOMAINS[category] && !CSP_DOMAINS[category].includes(domain)) {
    CSP_DOMAINS[category].push(domain);
    console.log(`已添加 ${domain} 到 ${category}`);
  } else {
    console.warn(`无效的类别 ${category} 或域名 ${domain} 已存在`);
  }
}

// 验证域名是否在允许列表中
export function isDomainAllowed(category, domain) {
  return CSP_DOMAINS[category]?.includes(domain) || false;
}

// 获取所有允许的域名（用于调试）
export function getAllowedDomains() {
  return CSP_DOMAINS;
}
